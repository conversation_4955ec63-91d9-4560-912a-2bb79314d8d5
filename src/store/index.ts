import * as React from "react";
import { LangStore } from "@/store/lang";
import { TokenStore } from "@/store/tokenStore";
import { HistoryStore } from "@/store/historyStore";
import { DepositStore } from "@/store/depositStore";
import { GlobalStore } from "@/store/globalStore";

export class RootStore {
  private _lang?: LangStore;
  private _tokenStore?: TokenStore;
  private _historyStore?: HistoryStore;
  private _depositStore?: DepositStore;
  private _globalStore?: GlobalStore;

  // Lazy initialization getters for better performance
  get lang(): LangStore {
    if (!this._lang) {
      this._lang = new LangStore();
    }
    return this._lang;
  }

  get tokenStore(): TokenStore {
    if (!this._tokenStore) {
      this._tokenStore = new TokenStore();
    }
    return this._tokenStore;
  }

  get historyStore(): HistoryStore {
    if (!this._historyStore) {
      this._historyStore = new HistoryStore();
    }
    return this._historyStore;
  }

  get depositStore(): DepositStore {
    if (!this._depositStore) {
      this._depositStore = new DepositStore();
    }
    return this._depositStore;
  }

  get globalStore(): GlobalStore {
    if (!this._globalStore) {
      this._globalStore = new GlobalStore();
    }
    return this._globalStore;
  }

  /**
   * Initialize all stores - useful for eager initialization when needed
   */
  async initialize(): Promise<void> {
    try {
      // Initialize language store first as it might be needed by others
      await this.lang.init();

      // Initialize other stores
      this.tokenStore;
      this.historyStore;
      this.depositStore;
      this.globalStore;
    } catch (error) {
      console.error("Failed to initialize stores:", error);
      throw error;
    }
  }

  /**
   * Dispose all stores - useful for cleanup
   */
  dispose(): void {
    // Clear any timers, subscriptions, etc. if stores have them
    this._lang = undefined;
    this._tokenStore = undefined;
    this._historyStore = undefined;
    this._depositStore = undefined;
    this._globalStore = undefined;
  }
}

// Create singleton instance
export const rootStore = new RootStore();

// Create typed React context
const StoresContext = React.createContext<RootStore>(rootStore);

// Custom hook with proper typing
export const useStore = (): RootStore => {
  const context = React.useContext(StoresContext);
  if (!context) {
    throw new Error("useStore must be used within a StoresContext.Provider");
  }
  return context;
};

// Provider component for easier usage
interface StoresProviderProps {
  children: React.ReactNode;
}

export const StoresProvider: React.FC<StoresProviderProps> = ({ children }) => {
  React.useEffect(() => {
    rootStore.initialize();
  }, []);

  return React.createElement(
    StoresContext.Provider,
    { value: rootStore },
    children,
  );
};

// Development helper - only attach to window in development
if (process.env.NODE_ENV === "development") {
  (window as any)._store = rootStore;
}
