import { makeAutoObservable } from "mobx";
import axios from "axios";
import Token from "@/types/token";
import { useReadContracts } from "wagmi";
import { wagmiAdapter } from '@/provider';
import { readContracts } from '@wagmi/core';

export class TokenStore {
  private readonly CACHE_DURATION = 3600000;
  private readonly CACHE_KEY = "token_list_cache";
  private readonly CACHE_TIMESTAMP_KEY = "token_list_cache_timestamp";

  tokenList: Token[] = [];

  constructor() {
    makeAutoObservable(this);
  }

  async getTokenList(chainId: number, destChainId: number) {
    const now = Date.now();
    const cacheKey = `${this.CACHE_KEY}_${chainId}_${destChainId}`;
    const timestampKey = `${this.CACHE_TIMESTAMP_KEY}_${chainId}_${destChainId}`;

    const cachedData = localStorage.getItem(cacheKey);
    const cachedTimestamp = localStorage.getItem(timestampKey);

    if (cachedData && cachedTimestamp) {
      const timestamp = parseInt(cachedTimestamp, 10);
      if (now - timestamp < this.CACHE_DURATION) {
        try {
          this.tokenList = JSON.parse(cachedData);
        } catch (error) {
          localStorage.removeItem(cacheKey);
          localStorage.removeItem(timestampKey);
        }
      }
    }

    try {
      const response = await axios.get(
        `http://localhost:9527/tube/getTokenList`,
        {
          params: {
            chainId,
            destChainId,
          },
        },
      );

      const tokens = response.data || [];

      if (tokens.length > 0) {
        localStorage.setItem(cacheKey, JSON.stringify(tokens));
        localStorage.setItem(timestampKey, now.toString());
      }

      this.tokenList = tokens;
    } catch (error) {
      console.error("Failed to fetch token list:", error);
      if (cachedData) {
        try {
          this.tokenList = JSON.parse(cachedData);
        } catch (parseError) {
          console.error(
            "Failed to parse cached token list as fallback:",
            parseError,
          );
        }
      }
      this.tokenList = [];
    }
  }

  async batchFetchTokenBalance(tokens: Token[], address: string) {
    const results = await readContracts(wagmiAdapter.wagmiConfig, {
      contracts,
    });
  }
}
