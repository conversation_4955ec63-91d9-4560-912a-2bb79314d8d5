import { useEffect, useState } from "react";
import { observer } from "mobx-react-lite";
import { useStore } from "@/store";
import { Button } from "@heroui/react";

/**
 * 示例组件，展示如何使用 store
 */
const StoreExample = observer(() => {
  const store = useStore();
  const [isLoading, setIsLoading] = useState(false);
  
  // 从各个 store 中获取数据
  const { lang, tokenStore, depositStore } = store;
  
  // 初始化语言
  useEffect(() => {
    const initLang = async () => {
      setIsLoading(true);
      try {
        await lang.init();
      } catch (error) {
        console.error("Failed to initialize language:", error);
      } finally {
        setIsLoading(false);
      }
    };
    
    initLang();
  }, [lang]);
  
  // 切换语言的处理函数
  const handleLanguageChange = async (newLang: string) => {
    setIsLoading(true);
    try {
      await lang.setLang(newLang);
    } catch (error) {
      console.error(`Failed to set language to ${newLang}:`, error);
    } finally {
      setIsLoading(false);
    }
  };
  
  // 获取代币列表
  const fetchTokens = async () => {
    if (depositStore.fromNetwork && depositStore.destNetwork) {
      await tokenStore.getTokenList(
        depositStore.fromNetwork.chainId,
        depositStore.destNetwork.chainId
      );
    }
  };
  
  return (
    <div className="p-4 bg-gray-800 rounded-lg text-white">
      <h2 className="text-xl font-bold mb-4">Store 示例</h2>
      
      {/* 语言部分 */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-2">当前语言: {lang.lang}</h3>
        <div className="flex gap-2">
          <Button 
            className="px-4 py-2 bg-blue-600 rounded disabled:opacity-50"
            disabled={isLoading || lang.lang === "en"}
            onClick={() => handleLanguageChange("en")}
          >
            English
          </Button>
          <Button 
            className="px-4 py-2 bg-blue-600 rounded disabled:opacity-50"
            disabled={isLoading || lang.lang === "zh_CN"}
            onClick={() => handleLanguageChange("zh_CN")}
          >
            中文
          </Button>
        </div>
        
        {/* 翻译示例 */}
        <div className="mt-4">
          <p>翻译示例: {lang.t("description" as any) || "Bridge"}</p>
        </div>
      </div>
      
      {/* 网络部分 */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-2">网络信息</h3>
        <p>源网络: {depositStore.fromNetwork?.name || "未设置"}</p>
        <p>目标网络: {depositStore.destNetwork?.name || "未设置"}</p>
        <Button 
          className="mt-2 px-4 py-2 bg-green-600 rounded"
          onClick={() => depositStore.swapNetworks()}
        >
          交换网络
        </Button>
      </div>
      
      {/* 代币部分 */}
      <div>
        <h3 className="text-lg font-semibold mb-2">代币列表</h3>
        <p>当前代币数量: {tokenStore.tokenList.length}</p>
        <Button 
          className="mt-2 px-4 py-2 bg-purple-600 rounded"
          onClick={fetchTokens}
        >
          刷新代币列表
        </Button>
        
        {tokenStore.tokenList.length > 0 && (
          <div className="mt-4 max-h-40 overflow-y-auto">
            <ul className="space-y-1">
              {tokenStore.tokenList.map((token, index) => (
                <li key={index} className="text-sm">
                  {token.symbol} - {token.name}
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
    </div>
  );
});

export default StoreExample;
