import { Route, Routes } from "react-router-dom";
import { useEffect, useState } from "react";
import { useStore } from "@/store";

import IndexPage from "@/pages/index";
import DocsPage from "@/pages/docs";
import PricingPage from "@/pages/pricing";
import BlogPage from "@/pages/blog";
import AboutPage from "@/pages/about";

// 加载组件
function LoadingScreen() {
  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-900">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
        <p className="text-white text-lg">正在初始化应用...</p>
      </div>
    </div>
  );
}

// 错误组件
function ErrorScreen({ error, onRetry }: { error: string; onRetry: () => void }) {
  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-900">
      <div className="text-center max-w-md">
        <div className="text-red-500 text-6xl mb-4">⚠️</div>
        <h1 className="text-white text-xl mb-4">应用初始化失败</h1>
        <p className="text-gray-300 mb-6">{error}</p>
        <button 
          onClick={onRetry}
          className="px-6 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          重试
        </button>
      </div>
    </div>
  );
}

function App() {
  const store = useStore();
  const [isInitialized, setIsInitialized] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const initializeApp = async () => {
    try {
      setError(null);
      setIsInitialized(false);
      
      // 初始化 stores
      await store.initialize();
      
      setIsInitialized(true);
    } catch (err) {
      console.error("App initialization failed:", err);
      setError(err instanceof Error ? err.message : "未知错误");
    }
  };

  useEffect(() => {
    initializeApp();
  }, []);

  // 错误状态
  if (error) {
    return <ErrorScreen error={error} onRetry={initializeApp} />;
  }

  // 加载状态
  if (!isInitialized) {
    return <LoadingScreen />;
  }

  // 正常应用
  return (
    <Routes>
      <Route element={<IndexPage />} path="/" />
      <Route element={<DocsPage />} path="/docs" />
      <Route element={<PricingPage />} path="/pricing" />
      <Route element={<BlogPage />} path="/blog" />
      <Route element={<AboutPage />} path="/about" />
    </Routes>
  );
}

export default App;
