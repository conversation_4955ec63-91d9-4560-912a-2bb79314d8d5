import { observer } from "mobx-react-lite";
import { useStore } from "@/store";
import { useStoreInitialization, useLangInitialization, useTokenInitialization } from "@/hooks/useStoreInitialization";
import { But<PERSON> } from "@heroui/react";

// 示例1: 使用完整的 Store 初始化
const FullAppComponent = observer(() => {
  const { isInitialized, isLoading, error, retry } = useStoreInitialization();

  if (error) {
    return (
      <div className="p-4 bg-red-100 border border-red-400 rounded">
        <h3 className="text-red-800 font-bold">初始化失败</h3>
        <p className="text-red-600">{error}</p>
        <Button onClick={retry} className="mt-2">
          重试
        </Button>
      </div>
    );
  }

  if (isLoading || !isInitialized) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mr-3"></div>
        <span>正在初始化应用...</span>
      </div>
    );
  }

  return (
    <div>
      <h2>应用已初始化完成</h2>
      {/* 你的应用内容 */}
    </div>
  );
});

// 示例2: 只初始化语言
const LanguageComponent = observer(() => {
  const store = useStore();
  const { isInitialized, isLoading, error } = useLangInitialization();

  if (error) {
    return <div className="text-red-500">语言加载失败: {error}</div>;
  }

  if (isLoading || !isInitialized) {
    return <div>正在加载语言...</div>;
  }

  return (
    <div>
      <p>当前语言: {store.lang.lang}</p>
      <p>翻译示例: {store.lang.t("description" as any) || "Bridge"}</p>
    </div>
  );
});

// 示例3: 初始化代币数据
const TokenComponent = observer(() => {
  const store = useStore();
  const fromChainId = store.depositStore.fromNetwork?.chainId;
  const destChainId = store.depositStore.destNetwork?.chainId;
  
  const { isInitialized, isLoading, error, retry } = useTokenInitialization(
    fromChainId,
    destChainId
  );

  if (error) {
    return (
      <div className="p-4 border border-red-300 rounded">
        <p className="text-red-600">代币加载失败: {error}</p>
        <Button onClick={retry} size="sm" className="mt-2">
          重新加载
        </Button>
      </div>
    );
  }

  if (isLoading || !isInitialized) {
    return (
      <div className="flex items-center">
        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500 mr-2"></div>
        <span>正在加载代币...</span>
      </div>
    );
  }

  return (
    <div>
      <h3>代币列表 ({store.tokenStore.tokenList.length})</h3>
      <ul className="max-h-40 overflow-y-auto">
        {store.tokenStore.tokenList.map((token, index) => (
          <li key={index} className="py-1">
            {token.symbol} - {token.name}
          </li>
        ))}
      </ul>
    </div>
  );
});

// 示例4: 组合使用多个初始化
const CombinedComponent = observer(() => {
  const store = useStore();
  
  // 先初始化语言
  const langInit = useLangInitialization();
  
  // 然后初始化代币（依赖于网络设置）
  const tokenInit = useTokenInitialization(
    store.depositStore.fromNetwork?.chainId,
    store.depositStore.destNetwork?.chainId
  );

  // 显示加载状态
  if (langInit.isLoading) {
    return <div>正在初始化语言...</div>;
  }

  if (langInit.error) {
    return (
      <div className="text-red-500">
        语言初始化失败: {langInit.error}
        <Button onClick={langInit.retry} size="sm" className="ml-2">
          重试
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <LanguageComponent />
      
      <div>
        <h3>网络信息</h3>
        <p>源网络: {store.depositStore.fromNetwork?.name}</p>
        <p>目标网络: {store.depositStore.destNetwork?.name}</p>
      </div>

      {tokenInit.isLoading && (
        <div>正在加载代币数据...</div>
      )}
      
      {tokenInit.error && (
        <div className="text-red-500">
          代币加载失败: {tokenInit.error}
          <Button onClick={tokenInit.retry} size="sm" className="ml-2">
            重试
          </Button>
        </div>
      )}
      
      {tokenInit.isInitialized && <TokenComponent />}
    </div>
  );
});

export { FullAppComponent, LanguageComponent, TokenComponent, CombinedComponent };
