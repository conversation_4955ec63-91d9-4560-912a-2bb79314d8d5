import { useEffect, useState } from "react";
import { observer } from "mobx-react-lite";
import { useStore } from "@/store";
import RootLayout from "@/layouts/RootLayout";
import { tabSelect, tabUnSelect } from "@/components/primitives.ts";
import { Button, Image } from "@heroui/react";
import Bridge from "@/components/Bridge";

const IndexPage = observer(() => {
  const store = useStore();
  const [isPageReady, setIsPageReady] = useState(false);
  const [initError, setInitError] = useState<string | null>(null);

  useEffect(() => {
    const initializePage = async () => {
      try {
        // 只初始化这个页面需要的 stores
        await store.lang.init(); // 初始化语言
        
        // 如果需要代币数据，可以预加载
        if (store.depositStore.fromNetwork && store.depositStore.destNetwork) {
          await store.tokenStore.getTokenList(
            store.depositStore.fromNetwork.chainId,
            store.depositStore.destNetwork.chainId
          );
        }
        
        setIsPageReady(true);
      } catch (error) {
        console.error("Page initialization failed:", error);
        setInitError(error instanceof Error ? error.message : "页面初始化失败");
      }
    };

    initializePage();
  }, [store]);

  if (initError) {
    return (
      <RootLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <p className="text-red-500 mb-4">页面加载失败: {initError}</p>
            <Button onClick={() => window.location.reload()}>
              重新加载
            </Button>
          </div>
        </div>
      </RootLayout>
    );
  }

  if (!isPageReady) {
    return (
      <RootLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
            <p>正在加载页面...</p>
          </div>
        </div>
      </RootLayout>
    );
  }

  return (
    <RootLayout>
      <section className="flex flex-col items-center justify-center pt-5">
        <div className="flex items-center justify-between w-full pb-5">
          <div>
            <Button className={tabSelect()} radius="full">
              Bridge
            </Button>
            <Button className={tabUnSelect()} radius="full">
              Buy
            </Button>
          </div>
          <div className="flex">
            <Image className="size-6" src="/public/icon_history.svg" />
            <Image className="size-6 ml-3" src="/public/icon_setting.svg" />
          </div>
        </div>
        <Bridge />
      </section>
    </RootLayout>
  );
});

export default IndexPage;
