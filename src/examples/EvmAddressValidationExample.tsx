import { useState } from "react";
import { Button, Input } from "@heroui/react";
import { 
  isValidEvmAddress, 
  validateAndFormatEvmAddress, 
  isEvmAddressEqual, 
  getShortEvmAddress 
} from "@/lib/utils/util";

/**
 * EVM 地址验证工具演示组件
 */
export default function EvmAddressValidationExample() {
  const [inputAddress, setInputAddress] = useState("");
  const [compareAddress1, setCompareAddress1] = useState("");
  const [compareAddress2, setCompareAddress2] = useState("");

  // 测试用的地址
  const testAddresses = [
    "0x742d35Cc6634C0532925a3b8D4C9db96C4b4Df8a", // 有效地址
    "0x742d35cc6634c0532925a3b8d4c9db96c4b4df8a", // 有效地址（小写）
    "0x742d35Cc6634C0532925a3b8D4C9db96C4b4Df", // 无效地址（太短）
    "742d35Cc6634C0532925a3b8D4C9db96C4b4Df8a", // 无效地址（没有0x前缀）
    "0xGGGd35Cc6634C0532925a3b8D4C9db96C4b4Df8a", // 无效地址（非十六进制字符）
    "0x0000000000000000000000000000000000000000", // 零地址（有效）
    "", // 空字符串
    "invalid-address", // 完全无效的地址
  ];

  const handleTestAddress = (address: string) => {
    setInputAddress(address);
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-2">EVM 地址验证工具</h1>
        <p className="text-gray-600">测试和验证以太坊虚拟机（EVM）地址的工具方法</p>
      </div>

      {/* 主要验证区域 */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-semibold mb-4">地址验证</h2>
        
        <div className="space-y-4">
          <Input
            label="输入 EVM 地址"
            placeholder="0x742d35Cc6634C0532925a3b8D4C9db96C4b4Df8a"
            value={inputAddress}
            onChange={(e) => setInputAddress(e.target.value)}
            className="w-full"
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* 验证结果 */}
            <div className="space-y-3">
              <h3 className="font-medium">验证结果：</h3>
              
              <div className="space-y-2 text-sm">
                <div className="flex items-center space-x-2">
                  <span className="font-medium">是否有效：</span>
                  <span className={`px-2 py-1 rounded text-xs ${
                    isValidEvmAddress(inputAddress) 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {isValidEvmAddress(inputAddress) ? "✅ 有效" : "❌ 无效"}
                  </span>
                </div>

                <div className="flex items-start space-x-2">
                  <span className="font-medium">校验和格式：</span>
                  <span className="break-all">
                    {validateAndFormatEvmAddress(inputAddress) || "无法格式化"}
                  </span>
                </div>

                <div className="flex items-center space-x-2">
                  <span className="font-medium">简短格式：</span>
                  <span>{getShortEvmAddress(inputAddress) || "无法简化"}</span>
                </div>

                <div className="flex items-center space-x-2">
                  <span className="font-medium">自定义简短格式：</span>
                  <span>{getShortEvmAddress(inputAddress, 8, 6) || "无法简化"}</span>
                </div>
              </div>
            </div>

            {/* 测试地址按钮 */}
            <div className="space-y-3">
              <h3 className="font-medium">测试地址：</h3>
              <div className="space-y-2">
                {testAddresses.map((address, index) => (
                  <Button
                    key={index}
                    size="sm"
                    variant="bordered"
                    className="w-full text-left justify-start text-xs"
                    onClick={() => handleTestAddress(address)}
                  >
                    <span className="truncate">
                      {address || "(空字符串)"}
                    </span>
                  </Button>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 地址比较区域 */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-semibold mb-4">地址比较</h2>
        
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="地址 1"
              placeholder="0x742d35Cc6634C0532925a3b8D4C9db96C4b4Df8a"
              value={compareAddress1}
              onChange={(e) => setCompareAddress1(e.target.value)}
            />
            <Input
              label="地址 2"
              placeholder="0x742d35cc6634c0532925a3b8d4c9db96c4b4df8a"
              value={compareAddress2}
              onChange={(e) => setCompareAddress2(e.target.value)}
            />
          </div>

          <div className="flex items-center space-x-2">
            <span className="font-medium">比较结果：</span>
            <span className={`px-3 py-1 rounded ${
              isEvmAddressEqual(compareAddress1, compareAddress2)
                ? 'bg-green-100 text-green-800'
                : 'bg-red-100 text-red-800'
            }`}>
              {isEvmAddressEqual(compareAddress1, compareAddress2) 
                ? "✅ 地址相同" 
                : "❌ 地址不同"}
            </span>
          </div>

          <div className="text-sm text-gray-600">
            <p>💡 提示：地址比较会忽略大小写差异</p>
          </div>
        </div>
      </div>

      {/* 使用说明 */}
      <div className="bg-gray-50 rounded-lg p-6">
        <h2 className="text-xl font-semibold mb-4">使用说明</h2>
        
        <div className="space-y-4 text-sm">
          <div>
            <h3 className="font-medium mb-2">可用的工具方法：</h3>
            <ul className="space-y-1 list-disc list-inside text-gray-700">
              <li><code className="bg-gray-200 px-1 rounded">isValidEvmAddress(address)</code> - 验证地址是否有效</li>
              <li><code className="bg-gray-200 px-1 rounded">validateAndFormatEvmAddress(address)</code> - 验证并格式化为校验和格式</li>
              <li><code className="bg-gray-200 px-1 rounded">isEvmAddressEqual(addr1, addr2)</code> - 比较两个地址是否相同</li>
              <li><code className="bg-gray-200 px-1 rounded">getShortEvmAddress(address, start?, end?)</code> - 获取简短显示格式</li>
            </ul>
          </div>

          <div>
            <h3 className="font-medium mb-2">导入方式：</h3>
            <pre className="bg-gray-200 p-2 rounded text-xs overflow-x-auto">
{`import { 
  isValidEvmAddress, 
  validateAndFormatEvmAddress, 
  isEvmAddressEqual, 
  getShortEvmAddress 
} from "@/lib/utils/util";`}
            </pre>
          </div>
        </div>
      </div>
    </div>
  );
}
